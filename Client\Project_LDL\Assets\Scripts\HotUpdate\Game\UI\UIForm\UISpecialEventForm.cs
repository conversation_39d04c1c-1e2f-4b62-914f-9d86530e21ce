using System;
using System.Collections.Generic;
using Activity;
using DG.Tweening;
using PbGameconfig;
using UnityEngine;
using activity_type = Game.Hotfix.Config.activity_type;

//特殊事件父界面
namespace Game.Hotfix
{
    public partial class UISpecialEventForm : UGuiFormEx
    {
        private UISwitchPage switchPage;
        private Dictionary<int, string> prefabList;
        
        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        private SpecialEventData SpecialEventManager => GameEntry.LogicData.SpecialEventData;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
             var targetIndex = 0;
            
            if (prefabList == null)
            {
                prefabList = new Dictionary<int, string>();
            }
            else
            {
                prefabList.Clear();
            }
            
            var list = new List<ValueTuple<int,string, string>>();//唯一id，名称，预制体
            var activityList = ChaoZhiManager.GetActivityList(2);
            var useList = new List<ActivityTime>();
            foreach (var activity in activityList)
            {
                var uniqueId = (int)activity.Template;
                var config = ChaoZhiManager.GetActivityConfig(uniqueId);
                if (config != null)
                {
                    var prefab = ChaoZhiManager.GetTemplatePrefab(uniqueId);
                    list.Add((uniqueId,ToolScriptExtend.GetLang(config.name),prefab));
                    useList.Add(activity);
                }
            }
            
            switchPage = m_goSwitchPage.GetComponent<UISwitchPage>();
            switchPage.EnumUIForm = EnumUIForm.UIChaoZhiActivityForm;
            for (var i = 0; i < useList.Count; i++)
            {
                switchPage.BindDataByIndex(i,useList[i]);
            }
            switchPage.OnInit(m_goTagItem, m_goTagRoot.transform, m_goPanelRoot.transform, list, OnSelectLogic);
            switchPage.SwitchTagGroup.ScrollToFirst();
           
            switchPage.SelectPageByIndex(targetIndex);
            switchPage.UseTimer = true;

            for (var i = 0; i < useList.Count; i++)
            {
                var data = useList[i];
                //显示标签图标
                var tagObj = switchPage.SwitchTagGroup.GetTagObjByIndex(i);
                if (tagObj != null)
                {
                    var icon = tagObj.transform.Find("btn/onBg/icon").GetComponent<UIImage>();
                    var config = ChaoZhiManager.GetActivityConfig((int)data.Template);
                    if (config != null)
                    {
                        if (config.activity_type == activity_type.activity_type_power)
                        {
                            icon.SetImage($"Sprite/ui_shijian/xwddh_icon1.png");
                        }
                    }
                }
            }
            
             // 初始化页签红点
             foreach (var activity in useList)
             {
                 var id = (int)activity.Template;
                 var count = SpecialEventManager.GetRedDotCountById(id);
                 switchPage.SwitchTagGroup.CheckNumDotLogic(id,count);
             }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            switchPage.OnRefresh(userData);
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "RedDot")
            {
                var count = SpecialEventManager.GetRedDotCountById(param);
                switchPage.SwitchTagGroup.CheckNumDotLogic(param,count);
            }
        }

        private void OnBtnExitClick()
        {
           Close();
        }
        
        private void OnSelectLogic(int index)
        {
            //显示标签图标
            var tagObj = switchPage.SwitchTagGroup.GetTagObjByIndex(index);
            if (tagObj != null)
            {
                var icon = tagObj.transform.Find("btn/onBg/icon");
                if (icon != null)
                {
                    icon.DOKill();
                    icon.DOScale(1.1f, 0.2f)
                        .SetEase(Ease.OutBack)
                        .OnComplete(() =>
                        {
                            icon.DOScale(1, 0.1f)
                                .SetEase(Ease.InOutQuad);
                        });
                }
            }
            
        }
    }
}
