using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityGameFramework.Runtime;
using System;
using GameFramework.Event;
using GameFramework;

namespace Game.Hotfix
{
    public class UGuiFormEx : UGuiForm
    {
        public bool enableMaskBackground = false;
        public float maskBackgroundAlpha = 0.8f;
        private UIButton maskBtn;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            RectTransform transform = GetComponent<RectTransform>();
            transform.anchorMin = Vector2.zero;
            transform.anchorMax = Vector2.one;
            transform.anchoredPosition3D = Vector3.zero;
            transform.sizeDelta = Vector2.zero;

            if (enableMaskBackground)
            {
                CreateMaskBackground();
            }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (maskBtn != null)
                maskBtn.enabled = true;
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            //UnSubscribeAll();
            //if (eventSubscriber != null)
            //{
            //    ReferencePool.Release(eventSubscriber);
            //    eventSubscriber = null;
            //}

            //HideAllItem();
            //if (itemLoader != null)
            //{
            //    ReferencePool.Release(itemLoader);
            //    itemLoader = null;
            //}
        }

        /// <summary>
        /// 创建黑色半透遮罩背景
        /// </summary>
        void CreateMaskBackground()
        {
            // 创建遮罩背景
            GameObject maskBackground = new("MaskBackground");
            maskBackground.transform.SetParent(transform, false);
            maskBackground.transform.SetAsFirstSibling();

            // 添加必要的组件
            var rectTrans = maskBackground.AddComponent<RectTransform>();
            rectTrans.anchorMin = Vector2.zero;
            rectTrans.anchorMax = Vector2.one;
            rectTrans.sizeDelta = Vector2.zero;
            rectTrans.localScale = Vector3.one;

            var image = maskBackground.AddComponent<UIImage>();
            image.color = new Color(0, 0, 0, maskBackgroundAlpha);

            // 添加点击事件
            maskBtn = maskBackground.AddComponent<UIButton>();
            maskBtn.useTween = false;
            maskBtn.onClick.AddListener(() =>
            {
                maskBtn.enabled = false;
                Close();
            });

            maskBackground.SetActive(true);
        }

        /// <summary>
        /// 递归设置粒子系统的渲染层级
        /// </summary>
        /// <param name="parent">目标游戏物体</param>
        /// <param name="depth">渲染顺序</param>
        public void SetParticleSystemSortingOrder(GameObject parent, int depth)
        {
            ToolScriptExtend.SetParticleSystemSortingOrder(parent, depth);
        }
    }
}
