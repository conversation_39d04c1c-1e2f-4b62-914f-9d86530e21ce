﻿/*
 * DynamicScrollView.cs
 *
 * <AUTHOR> / https://github.com/mosframe
 *
 */
using DG.Tweening;

namespace Mosframe
{

    using System.Collections.Generic;
    using UnityEngine;
    using UnityEngine.UI;
    using UnityEngine.EventSystems;
    using System.Collections;
    using System;

    /// <summary>
    /// Dynamic Scroll View
    /// </summary>
    [RequireComponent(typeof(ScrollRect))]
    public abstract class TableView : UIBehaviour
    {

        private int totalItemCount = 99;
        protected int TotalItemCount => totalItemCount;
        private int extendLength = 0;
        protected int ExtendLength => extendLength;
        [HideInInspector]
        public RectTransform itemPrototype = null;

        public delegate T DeleteCall<T>();
        public DeleteCall<int> GetItemCount = null;
        public DeleteCall<GameObject> GetItemGo = null;
        public Action<int, GameObject> UpdateItemCell = null;
        public Action<GameObject, object> CellOnClicked = null;

        public bool anime = false;
        public float duration = 0.08f;
        public int PageCount = -1; //一頁多少

        public bool isPlaying { set { value = anime; } get { return anime; } }

        public void scrollToLastPos()
        {

            this.contentAnchoredPosition = this.viewportSize - this.contentSize;
            this.refresh();
        }
        public void scrollByItemIndex(int itemIndex)
        {
            var totalLen = this.contentSize - extendLength;
            var itemLen = totalLen / this.totalItemCount;
            var pos = itemLen * itemIndex;
            this.contentAnchoredPosition = -pos;
        }
        
        public void SetCenterPos()
        {
            var totalLen = this.contentSize - extendLength;
            this.contentAnchoredPosition = totalLen * 0.5f;
        }

        public void refreshByPos(float toPos = 0.0f, int extendLength = 0)
        {
            InitTableViewByPos(toPos, extendLength);
        }

        public void refreshByIndex(int startIndex = 0, int extendLength = 0)
        {
            InitTableViewByIndex(startIndex, extendLength);
        }

        public void refresh()
        {
            if (this.itemPrototype == null)
                return;
            if (isPlaying) return;
            var index = 0;
            if (this.contentAnchoredPosition != 0)
            {
                index = (int)(-this.contentAnchoredPosition / this.itemSize);
            }

            foreach (var itemRect in this.containers)
            {

                // set item position
                var pos = this.itemSize * index;
                itemRect.anchoredPosition = (this.direction == Direction.Vertical) ? new Vector2(0, -pos) : new Vector2(pos, 0);

                this.updateItem(index, itemRect.gameObject);

                ++index;
            }

            this.nextInsertItemNo = index - this.containers.Count;
            this.prevAnchoredPosition = (int)(this.contentAnchoredPosition / this.itemSize) * this.itemSize;
        }

        public void UpdateTableView()
        {
            if (isPlaying) return;
            var index = 0;

            foreach (var itemRect in this.containers)
            {
                // set item position
                var pos = this.itemSize * index;
                itemRect.anchoredPosition = (this.direction == Direction.Vertical) ? new Vector2(0, -pos) : new Vector2(pos, 0);

                this.updateItem(index, itemRect.gameObject);

                ++index;
            }
        }

        public void StopScrollView()
        {
            this.scrollRect.velocity = Vector2.zero;
        }

        protected override void Awake()
        {

            //             if( this.itemPrototype == null ) {
            //                 Debug.LogError( RichText.Red(new{this.name,this.itemPrototype}) );
            //                 return;
            //             }

            base.Awake();

            this.scrollRect = this.GetComponent<ScrollRect>();
            this.viewportRect = this.scrollRect.viewport;
            this.contentRect = this.scrollRect.content;
            isPlaying = anime;
            // OnDestroy();
        }
        protected override void Start()
        {
            this.prevTotalItemCount = this.totalItemCount;
        }

        protected override void OnDisable()
        {
            // OnDestroy();
        }

        protected override void OnDestroy()
        {
            itemPrototype = null;
            GetItemCount = null;
            GetItemGo = null;
            UpdateItemCell = null;
            CellOnClicked = null;
            isPlaying = anime;
        }

        public void ReloadData()
        {
            if (GetItemCount != null)
            {
                totalItemCount = GetItemCount();
            }
            this.resizeContent();
            this.refresh();
        }

        public void InitTableViewByPos(float toPos = 0.0f, int extendLength = 0)
        {
            InitTableView(0, extendLength, toPos);
        }

        public void InitTableViewByIndex(int startIndex = 0, int extendLength = 0)
        {
            InitTableView(startIndex, extendLength, 0);
        }

        private void InitTableView(int startIndex = 0, int length = 0, float toPos = 0.0f)
        {
            extendLength = length;
            if (GetItemCount != null)
            {
                totalItemCount = GetItemCount();
            }

            if (totalItemCount <= 0)
            {
                this.clearTableData();
                return;
            }

            if (GetItemGo != null)
            {
                itemPrototype = GetItemGo().GetComponent<RectTransform>();
            }

            int scrollPosIndex = startIndex;
            int offset = totalItemCount - 1 - startIndex;
            if (offset <= 0)
            {
                startIndex = totalItemCount - 3;
                if (offset < 0)
                    scrollPosIndex = totalItemCount - 1;
            }
            else if (offset == 1)
                startIndex -= 1;
            this.clearTableData();
            this.ShowPrefab(startIndex, scrollPosIndex, toPos);
            if (!isPlaying)
            {
                this.refresh();
            }
        }

        private void ShowPrefab(int startIndex, int scrollPosIndex, float toPos = 0.0f)
        {
            // hide prototype

            this.itemPrototype.gameObject.SetActive(false);

            // instantiate items

            var itemCount = (int)(this.viewportSize / this.itemSize) + 3;

            // resize content
            this.resizeContent();

            int animeCount = 0;
            for (var i = 0; i < itemCount; ++i)
            {
                var itemRect = Instantiate(this.itemPrototype);
                var rectTrans = itemRect.GetComponent<RectTransform>();
                var oldSize = rectTrans.sizeDelta;
                itemRect.SetParent(this.contentRect, false);

                Button[] btnArray = itemRect.GetComponentsInChildren<Button>();
                foreach (Button btn in btnArray)
                {
                    btn.onClick.RemoveAllListeners();
                    btn.onClick.AddListener(() =>
                    {
                        if (CellOnClicked != null)
                        {
                            CellOnClicked(btn.gameObject, null);
                        }
                    });
                }

                if (this.direction == Direction.Horizontal) itemRect.setSizeFromLeft(1.0f); else itemRect.setSizeFromTop(1.0f);

                itemRect.name = i.ToString();
                this.containers.AddLast(itemRect);

                itemRect.gameObject.SetActive(true);
                if (isPlaying)
                {
                    this.updateItem(i, itemRect.gameObject);
                    Vector2 tarPos = this.direction == Direction.Vertical ? new Vector2(0, -this.itemSize * i) : new Vector2(this.itemSize * i, 0);
                    float width = this.contentRect.sizeDelta.x;
                    Vector2 initPos = this.direction == Direction.Vertical ? new Vector2(0, -width) : new Vector2(width, 0);
                    itemRect.anchoredPosition = initPos;
                    itemRect.transform.DOLocalMoveX(tarPos.x, 0.5f).SetDelay(i * duration).OnComplete(() =>
                    {
                        if (++animeCount >= itemCount)
                        {
                            anime = false;
                        }
                    });
                }

            }

            //go to target Index
            this.scrollByItemIndex(scrollPosIndex);

            if (toPos > 0 || toPos < 0)
            {
                this.contentAnchoredPosition = this.contentAnchoredPosition + toPos;
            }
        }

        private void clearTableData()
        {
            for (int i = 0; i < this.contentRect.childCount; i++)
            {
                var go = contentRect.GetChild(i).gameObject;
                var name = go.name;
                if (name.CompareTo(i.ToString()) == 1)
                    continue;
                Destroy(go);
            }
            containers.Clear();
        }


        private void Update()
        {
            if (itemPrototype == null) return;

            if (isPlaying) return;

            // [ Scroll up ]

            while (this.contentAnchoredPosition - this.prevAnchoredPosition < -this.itemSize * 2)
            {

                this.prevAnchoredPosition -= this.itemSize;

                // move a first item to last

                var first = this.containers.First;
                if (first == null) break;
                var item = first.Value;
                this.containers.RemoveFirst();
                this.containers.AddLast(item);

                // set item position

                var pos = this.itemSize * (this.containers.Count + this.nextInsertItemNo);
                item.anchoredPosition = (this.direction == Direction.Vertical) ? new Vector2(0, -pos) : new Vector2(pos, 0);

                // update item

                this.updateItem(this.containers.Count + this.nextInsertItemNo, item.gameObject);

                this.nextInsertItemNo++;
            }

            // [ Scroll down ]

            while (this.contentAnchoredPosition - this.prevAnchoredPosition > 0)
            {

                this.prevAnchoredPosition += this.itemSize;

                // move a last item to first

                var last = this.containers.Last;
                if (last == null) break;
                var item = last.Value;
                this.containers.RemoveLast();
                this.containers.AddFirst(item);

                this.nextInsertItemNo--;

                // set item position

                var pos = this.itemSize * this.nextInsertItemNo;
                item.anchoredPosition = (this.direction == Direction.Vertical) ? new Vector2(0, -pos) : new Vector2(pos, 0);

                // update item

                this.updateItem(this.nextInsertItemNo, item.gameObject);
            }
        }

        private void resizeContent()
        {
            if (this.itemPrototype == null)
                return;
            var size = this.contentRect.getSize();
            if (this.direction == Direction.Vertical) size.y = this.itemSize * this.totalItemCount + extendLength;
            else
            {
                int count = totalItemCount;
                if (PageCount > 0 && totalItemCount < PageCount)
                {
                    if (contentRect.transform.GetComponent<HorizontalLayoutGroup>() != null)
                        contentRect.transform.GetComponent<HorizontalLayoutGroup>().enabled = true;
                    count = PageCount;
                }
                else
                {
                    if (contentRect.transform.GetComponent<HorizontalLayoutGroup>() != null)
                        contentRect.transform.GetComponent<HorizontalLayoutGroup>().enabled = false;
                }
                size.x = this.itemSize * count + extendLength;

            }
            this.contentRect.setSize(size);
        }
        private void updateItem(int index, GameObject itemObj)
        {

            if (index < 0 || index >= this.totalItemCount)
            {

                itemObj.SetActive(false);
            }
            else
            {

                itemObj.SetActive(true);

                var item = itemObj.GetComponent<TableViewCell>();
                if (item != null) item.onUpdateItem(index);
                UpdateItemCell?.Invoke(index, itemObj);
            }
        }

        public void OnPointerUp()
        {

        }
        public void OnPointerDown()
        {

        }

        [ContextMenu("Initialize")]
        public virtual void init()
        {

            // [ cliear ]

            this.clear();

            // [ RectTransform ]

            var rectTransform = this.GetComponent<RectTransform>();
            rectTransform.setFullSize();

            // [ ScrollRect ]

            var scrollRect = this.GetComponent<ScrollRect>();
            scrollRect.horizontal = this.direction == Direction.Horizontal;
            scrollRect.vertical = this.direction == Direction.Vertical;
            scrollRect.scrollSensitivity = 15f;

            // [ ScrollRect / Viewport ]

            var viewportRect = new GameObject("Viewport", typeof(RectTransform), typeof(Mask), typeof(Image)).GetComponent<RectTransform>();
            viewportRect.SetParent(scrollRect.transform, false);
            viewportRect.setFullSize();
            viewportRect.offsetMin = new Vector2(10f, 10f);
            viewportRect.offsetMax = new Vector2(-10f, -10f);
            var viewportImage = viewportRect.GetComponent<Image>();
            //viewportImage.sprite = UnityEditor.AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UIMask.psd");
            viewportImage.type = Image.Type.Sliced;
            var viewportMask = viewportRect.GetComponent<Mask>();
            viewportMask.showMaskGraphic = false;
            scrollRect.viewport = viewportRect;

            // [ ScrollRect / Viewport / Content ]

            var contentRect = new GameObject("Content", typeof(RectTransform)).GetComponent<RectTransform>();
            contentRect.SetParent(viewportRect, false);
            if (this.direction == Direction.Horizontal) contentRect.setSizeFromLeft(1.0f); else contentRect.setSizeFromTop(1.0f);
            var contentRectSize = contentRect.getSize();
            contentRect.setSize(contentRectSize - contentRectSize * 0.06f);
            scrollRect.content = contentRect;


            // [ ScrollRect / Viewport / Content / PrototypeItem ]

            //this.resetPrototypeItem( contentRect );


            // [ ScrollRect / Scrollbar ]

            var scrollbarName = this.direction == Direction.Horizontal ? "Scrollbar Horizontal" : "Scrollbar Vertical";
            var scrollbarRect = new GameObject(scrollbarName, typeof(Scrollbar), typeof(Image)).GetComponent<RectTransform>();
            scrollbarRect.SetParent(viewportRect, false);
            if (this.direction == Direction.Horizontal) scrollbarRect.setSizeFromBottom(0.05f); else scrollbarRect.setSizeFromRight(0.05f);
            scrollbarRect.SetParent(scrollRect.transform, true);

            var scrollbar = scrollbarRect.GetComponent<Scrollbar>();
            scrollbar.direction = (this.direction == Direction.Horizontal) ? Scrollbar.Direction.LeftToRight : Scrollbar.Direction.BottomToTop;
            if (this.direction == Direction.Horizontal) scrollRect.horizontalScrollbar = scrollbar; else scrollRect.verticalScrollbar = scrollbar;

            // [ ScrollRect / Scrollbar / Image ]

            var scrollbarImage = scrollbarRect.GetComponent<Image>();
#if UNITY_EDITOR
            scrollbarImage.sprite = UnityEditor.AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/Background.psd");
#endif
            scrollbarImage.color = new Color(0.1f, 0.1f, 0.1f, 0.5f);
            scrollbarImage.type = Image.Type.Sliced;

            // [ ScrollRect / Scrollbar / slidingArea ]

            var slidingAreaRect = new GameObject("Sliding Area", typeof(RectTransform)).GetComponent<RectTransform>();
            slidingAreaRect.SetParent(scrollbarRect, false);
            slidingAreaRect.setFullSize();

            // [ ScrollRect / Scrollbar / slidingArea / Handle ]

            var scrollbarHandleRect = new GameObject("Handle", typeof(Image)).GetComponent<RectTransform>();
            scrollbarHandleRect.SetParent(slidingAreaRect, false);
            scrollbarHandleRect.setFullSize();
            var scrollbarHandleImage = scrollbarHandleRect.GetComponent<Image>();
#if UNITY_EDITOR
            scrollbarHandleImage.sprite = UnityEditor.AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UISprite.psd");
#endif
            scrollbarHandleImage.color = new Color(0.5f, 0.5f, 1.0f, 0.5f);
            scrollbarHandleImage.type = Image.Type.Sliced;
            scrollbar.handleRect = scrollbarHandleRect;

            // [ ScrollRect / ScrollbarHandleSize ]

            var scrollbarHandleSize = scrollRect.GetComponent<ScrollbarHandleSize>();
            if (scrollbarHandleSize == null)
            {
                scrollbarHandleSize = scrollRect.gameObject.AddComponent<ScrollbarHandleSize>();
                scrollbarHandleSize.maxSize = 1.0f;
                scrollbarHandleSize.minSize = 0.1f;
            }

            // [ Layer ]

            this.gameObject.setLayer(this.transform.parent.gameObject.layer, true);
        }
        protected virtual void clear()
        {

            while (this.transform.childCount > 0)
            {
                DestroyImmediate(this.transform.GetChild(0).gameObject);
            }
        }
        
        


        protected abstract float contentAnchoredPosition { get; set; }
        protected abstract float contentSize { get; }
        protected abstract float viewportSize { get; }
        protected abstract float itemSize { get; }


        protected Direction direction = Direction.Vertical;
        protected LinkedList<RectTransform> containers = new LinkedList<RectTransform>();
        protected float prevAnchoredPosition = 0;
        protected int nextInsertItemNo = 0; // item index from left-top of viewport at next insert
        protected int prevTotalItemCount = 99;
        protected ScrollRect scrollRect = null;
        protected RectTransform viewportRect = null;
        protected RectTransform contentRect = null;




        /// <summary> Scroll Direction </summary>
        public enum Direction
        {
            Vertical,
            Horizontal,
        }
    }
}
