using System;
using System.Collections.Generic;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using Spine.Unity;

namespace Game.Hotfix
{
    public class ChaoZhi_RoleUpStar:SwitchPanelLogic
    {
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private UIButton m_btnHelp;
        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private Transform m_transRole;
        [SerializeField] private UIButton m_btnGet;
        [SerializeField] private UIButton m_btnGray;
        [SerializeField] private UIText m_txtTimer; //倒计时文本
        [SerializeField] private ScrollRect scrollList;
        private Transform rewardRoot;
        private int timerCount; //倒计时数值
        private ChaoZhiData Manager;
        private bool isTimer = false;
        private dailydealshowtype showType = dailydealshowtype.dailydealshowtype_1;
        [SerializeField] private SkeletonGraphic m_spuiRole1;
        [SerializeField] private SkeletonGraphic m_spuiRole2;
        [SerializeField] private SkeletonGraphic m_spuiRole3;

        [SerializeField] private GameObject m_goRoleItem;

        [SerializeField] private GameObject m_goType1;
        [SerializeField] private GameObject m_goType2;
        [SerializeField] private GameObject m_goType3;
        [SerializeField] private GameObject m_goRewardBarEffect;
        [SerializeField] private UIText m_txtTip;
        private ActivityTime activityMsg;
        protected int TemplateId;
        private PushActivityHeroStarConfig ConfigData;
        private PushActivityHeroStarData MsgData;
        private bool isAllReceived = false;
        
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            Manager = GameEntry.LogicData.ChaoZhiData;
            isAllReceived = false;
            isTimer = false;
            rewardRoot = scrollList.content.transform;
            
            // InitPageView();
            //  
            //购买所有礼包按钮
            BindBtnLogic(m_btnGet, () =>
            {
                // var taskId = GetCurTaskId();
                var isCanGet = Manager.IsTaskHeroStarFinish(MsgData);
                if (!isCanGet)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1100426)//目前没有可领取奖励
                    });
                    return;
                }
                var reqData = new ActivityDrawReq();
                reqData.Type = activityMsg.Type;
                reqData.Template = MsgData.Template;
                reqData.LoopTimes = MsgData.LoopTimes;
                reqData.DrawId = GetCurTaskId();
                
                Manager.C2SActivityDrawReq(reqData, (resp) =>
                {
                    
                });
            });
            
            var bindData = GetBindData();
            if (bindData != null)
            {
                if (bindData is ActivityTime data)
                {
                    activityMsg = data;
                    InitFormData();
                    Manager.C2SLoadActivityInfo(activityMsg);
                }
            }
        }

        private void InitFormData()
        {
            TemplateId = (int)activityMsg.Template;
            ConfigData = Manager.GetHeroUpStarConfig(TemplateId);
            MsgData = Manager.GetHeroStarCache(TemplateId);
            InitPageView();
        }
        
        //再次打开
        public override void OnReOpen()
        {
            
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
        }


        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "ChaoZhi_RoleUpStar")
            {
                //1：英雄星级更新 2：活动信息更新
                if (param == 1)
                {
                    InitPageView();
                }
                else if (param == 2)
                {
                    InitFormData();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                    m_txtTimer.text = "00:00:00";
                }
            }
        }

        //资源释放
        public override void Release()
        {
            
        }

        private void InitPageView()
        {
            if (MsgData == null || ConfigData == null) return;
            var TemplateId = (int)MsgData.Template;
            if (!ToolScriptExtend.GetTable<activity_main>(out var table)) return;
            var activityMain = table.FirstOrDefault(x => (int)x.activity_template == TemplateId);
            if(activityMain == null)return;
            //活动标题
            m_txtTitle.text = ToolScriptExtend.GetLang(activityMain.name);
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                Manager.ShowActivityTip(TemplateId);
            });
            
            //计算倒计时
            timerCount = Manager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
            
            var realTaskData = GetCurTaskData();
            if (realTaskData == null) return;
            var id = GetCurTaskId();
            
            var tables = GameEntry.LDLTable.GetTable<activity_herostar_condition>();
            var config = tables.FirstOrDefault(x => (int)x.id == id);
            if (config == null) return;
            
            List<(int,int)> heroList = new List<(int, int)>();
            var heroIdCount = ConfigData.HeroIds.Count;
            var starConditionCount = realTaskData.StarConditions.Count;
            if (heroIdCount != starConditionCount)
            {
                ColorLog.Red("英雄数量和星级要求条件个数不对应！请检查配置！");
                return;
            }
            
            for (var i = 0; i < heroIdCount; i++)
            {
                heroList.Add(((int)ConfigData.HeroIds[i],realTaskData.StarConditions[i]));
            }
            //角色列表
            ShowRoleList(heroList);
            isAllReceived = Manager.IsUpStarAllReceived(ConfigData,MsgData);
            //奖励列表
            bool isCanReceive = Manager.IsTaskHeroStarFinish(MsgData);
            
            var rewardList = new List<reward>();
            foreach (var node in realTaskData.Rewards)
            {
                rewardList.Add(new reward() { item_id = (itemid)node.ItemId, num = node.Num });
            }
            ShowRewardList(m_goReward,rewardRoot,rewardList,isCanReceive,0.7f);
            m_txtTip.text = ToolScriptExtend.GetLang(isAllReceived ? 1100429 : 1100386);
            // 判断角色升星奖励是否可领取
            var isOk = !isAllReceived && isCanReceive;
            m_btnGet.gameObject.SetActive(isOk);//可领取
            m_btnGray.gameObject.SetActive(!isAllReceived && !isCanReceive);//已领取或未达标准
            m_goRewardBarEffect.SetActive(isOk);
        }

        private void ShowRoleList(List<(int,int)> list)
        {
            var idList = new List<int>();
            ToolScriptExtend.RecycleOrCreate(m_goRoleItem,m_transRole,list.Count);
            for (var i = 0; i < list.Count; i++)
            {
                var child = m_transRole.GetChild(i);
                var info = list[i];
                var heroId = info.Item1;
                idList.Add(heroId);
                SetRoleItemInfo(child.gameObject, heroId, info.Item2/5);
            }
            SetSpineInfo(idList);
        }
        
        private void SetRoleItemInfo(GameObject obj,int heroId,int starCount)
        {
            var config = ToolScriptExtend.GetItemConfig(heroId);
            if (config == null) return;
            
            CheckMultiLanguage(obj);
            var root = obj.transform;
            //英雄名称
            var roleName = root.Find("name").GetComponent<UIText>();
            //背景图
            var roleBg = root.Find("heroBg").GetComponent<UIImage>();
            //英雄图片
            var roleIcon = root.Find("heroMask/heroSp").GetComponent<UIImage>();
            //领取要求描述
            var roleDesc = root.Find("desc").GetComponent<UIText>();
            //星级
            var starObj = root.Find("starBg/starObj");
            var btn = root.Find("btn").GetComponent<UIButton>();
            
            roleName.text = ToolScriptExtend.GetLang(config.name);
            roleDesc.text = ToolScriptExtend.GetLangFormat(1100388,starCount.ToString());
            roleIcon.SetImage(config.icon);
            
            //判断是否完成领奖要求
            var isOk = false;
            var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
            if (heroVo != null)
            {
                var starNum = heroVo.StarNum;
                var starOrder = heroVo.StarOrder;
                SetStarView(starObj, starOrder, starNum);
                roleIcon.SetImage(heroVo.HeroHead);
                roleBg.SetImage(GetItemBgPath(heroVo.Quality));
                isOk = starNum >= starCount;
            }
            else
            {
                if (ToolScriptExtend.GetConfigById<hero_config>(heroId, out var heroConfig))
                {
                    var starNum = 0;
                    var starOrder = 0;
                    SetStarView(starObj, starOrder, starNum);
                    roleIcon.SetImage(heroConfig.hero_head);
                    roleBg.SetImage(GetItemBgPath(heroConfig.quality));
                }
            }
            
            var effect = root.Find("effect");
            effect.gameObject.SetActive(isOk);
            btn.gameObject.SetActive(!isOk);
            if (isOk)
            {
                var form = GetBindForm();
                if (form != null)
                {
                    ToolScriptExtend.SetParticleSystemSortingOrder(effect.gameObject,form.Depth);
                }
            }
            BindBtnLogic(btn, () =>
            {
                GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroId,panelIndex = 2);
            });
        }
        
        private void SetSpineInfo(List<int> idList)
        {
            var roleCount = idList.Count;
            
            m_spuiRole1.gameObject.SetActive(roleCount >= 1);
            m_spuiRole2.gameObject.SetActive(roleCount >= 2);
            m_spuiRole3.gameObject.SetActive(roleCount >= 3);
            
            m_goType1.SetActive(roleCount == 1);
            m_goType2.SetActive(roleCount == 2);
            m_goType3.SetActive(roleCount == 3);
            
            if (roleCount >= 1)
            {
                SetSpineMsg(idList[0], m_spuiRole1);
            }
            
            if (roleCount >= 2)
            {
                SetSpineMsg(idList[1], m_spuiRole2);
            }

            if (roleCount >= 3)
            {
                SetSpineMsg(idList[2], m_spuiRole3);
            }
            
            
            Transform targetRoot = null;
            if (roleCount == 1)
            {
                targetRoot = m_goType1.transform;
            }
            else if (roleCount == 2)
            {
                targetRoot = m_goType2.transform;
            }
            else if (roleCount == 3)
            {
                targetRoot = m_goType3.transform;
            }
            
            SetSpinePos(targetRoot, idList);
        }
        
        private void SetSpineMsg(int heroId,SkeletonGraphic graphic)
        {
            if (ToolScriptExtend.GetConfigById<hero_config>(heroId, out var data))
            {
                ToolScriptExtend.ShowHeroSpine(heroId, graphic);
            }
        }
        
        private void SetSpinePos(Transform root,List<int> idList)
        {
            var roleCount = idList.Count;
            if (roleCount >= 1)
            {
                ResetSpineNode(m_spuiRole1, root.Find("pos1"), idList[0]);
            }
            if (roleCount >= 2)
            {
                ResetSpineNode(m_spuiRole2, root.Find("pos2"), idList[1]);
            }
            if (roleCount >= 3)
            {
                ResetSpineNode(m_spuiRole3, root.Find("pos3"), idList[2]);
            }
        }
        
        private void ResetSpineNode(SkeletonGraphic graphic,Transform root,int heroId)
        {
            var trans = graphic.transform;
            trans.SetParent(root);
            trans.localScale = Vector3.zero;
            ToolScriptExtend.SetSpinePosAndScale(heroId,graphic.gameObject,3);
        }
        
        private string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di2_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di2_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
            };
        }

        private string GetStarBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di1_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di1_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
            };
        }

        private void SetStarView(Transform starRoot,int starOrder,int starNum)
        {
            var count = starRoot.childCount;
            for (int i = 0; i < count; i++)
            {
                var starSp = starRoot.GetChild(i).GetComponent<UIImage>();
                string pathStr;
                if (i < starNum) { pathStr = "Sprite/ui_hero/hero_icon_star5.png"; }
                else if (i < starNum + 1 && starOrder > 0) { pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder); }
                else { pathStr = "Sprite/ui_hero/hero_icon_star0.png"; }
                starSp.SetImage(pathStr);
            }
        }
        
        /// <summary>
        /// 展示奖励列表
        /// </summary>
        /// <param name="template">奖励模板</param>
        /// <param name="rewardRoot">奖励item生成的父节点</param>
        /// <param name="rewardList">奖励列表数据</param>
        /// <param name="Scaling">奖励item缩放</param>
        /// <param name="bigFontScale">放大itemObj上的文本组件,解决缩放itemObj后文本显示太小的问题</param>
        private void ShowRewardList(GameObject template,Transform rewardRoot,List<reward> rewardList,bool isCanGet,float Scaling = 0.5f,float bigFontScale = 1.3f)
        {
            ToolScriptExtend.RecycleOrCreate(template,rewardRoot,rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = rewardRoot.GetChild(i);
                SetRewardInfo(child, rewardList[i], Scaling, bigFontScale,isCanGet);
            }
        }
        
        //显示奖励信息
        private void SetRewardInfo(Transform root, reward info,float Scaling,float bigFontScale,bool isCanGet)
        {
            var data = ToolScriptExtend.GetItemConfig(info.item_id);
            if (data == null) return;
            
            //道具item
            var node = root.Find("node");
            node.localScale = Vector3.one * Scaling;
            if (node.childCount == 0)
            {
                BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
                {
                    module.SetClick(module.OpenTips);
                    ToolScriptExtend.SetItemObjTxtScale(module.gameObject,bigFontScale);
                    ToolScriptExtend.SetGameObjectGrey(module.transform,isAllReceived);
                });
            }
            else
            {
                var obj = node.GetChild(0).gameObject;
                ToolScriptExtend.SetItemObjInfo(node,obj,info.item_id,(int)info.num);
                ToolScriptExtend.SetGameObjectGrey(node,isAllReceived);
            }
            
            var effect = root.Find("effect");
            effect.gameObject.SetActive(isCanGet);
        }
        
        private ActivityHeroStarCondition GetCurTaskData()
        {
            var data = ConfigData.Conditions.FirstOrDefault(x => !MsgData.DrawIds.Contains(x.Id));
            if (data != null)
            {
                return data;
            }
            else
            {
                var count = ConfigData.Conditions.Count;
                if (count > 0 && count == MsgData.DrawIds.Count)
                {
                    return ConfigData.Conditions.LastOrDefault();
                }
            }
            return null;
        }
        
        private int GetCurTaskId()
        {
            var curData = GetCurTaskData();
            if (curData != null)
            {
                return (int)curData.Id;
            }
            return 0;
        }
    }
}

