﻿/*
 * DynamicVScrollView.cs
 *
 * <AUTHOR> / https://github.com/mosframe
 *
 */

using DG.Tweening;

namespace Mosframe {

    using UnityEngine;

    /// <summary>
    /// Dynamic Vertical Scroll View
    /// </summary>
    [AddComponentMenu("UI/TableViewV")]
    public class TableViewV : TableView {

        protected override float contentAnchoredPosition    { get { return -this.contentRect.anchoredPosition.y; } set { this.contentRect.anchoredPosition = new Vector2( this.contentRect.anchoredPosition.x, -value ); } }
	    protected override float contentSize                { get { return this.contentRect.rect.height; } }
	    protected override float viewportSize               { get { return this.viewportRect.rect.height;} }
	    protected override float itemSize                   { get { return this.itemPrototype.rect.height;} }

        public override void init () {

            this.direction = Direction.Vertical;
            base.init();
        }
        protected override void Awake() {

            base.Awake();
            this.direction = Direction.Vertical;
        }
        protected override void Start () {

            base.Start();
        }
        
        //用动画滚动到指定索引
        public void MoveToTargetIndex(int itemIndex,float duration = 0.5f)
        {
            var totalLen = contentSize - ExtendLength;
            var itemLen = totalLen / TotalItemCount;
            var pos = itemLen * itemIndex;

            var deadline = totalLen - scrollRect.viewport.rect.height;
            if (deadline < 0)
            {
                deadline = 0;
            }
            
            if (pos > deadline)
            {
                pos = deadline;
            }
            scrollRect.content.DOAnchorPos(new Vector2(0,pos), duration);
        }
        
    }
}
