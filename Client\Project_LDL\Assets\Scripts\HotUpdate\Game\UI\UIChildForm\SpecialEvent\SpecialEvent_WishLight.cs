using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using Mosframe;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class SpecialEvent_WishLight : SwitchPanelLogic
    {
        [SerializeField] private UIText m_txtTimer;
        [SerializeField] private UIButton m_btnUp;
        [SerializeField] private UIText m_txtValue;
        [SerializeField] private Slider m_sliderProgress;
        [SerializeField] private UIButton m_btnHelp;
        
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private TableViewV m_TableViewV;
        [SerializeField] private GameObject m_goTaskItem;
        [SerializeField] private GameObject m_goReward;
        
        private WaitForSeconds delayTime = new WaitForSeconds(0.2f);
        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        private SpecialEventData SpecialEventManager => GameEntry.LogicData.SpecialEventData;
        
        private ActivityTime activityMsg;
        private int TemplateId;
        private PushActivityPowerConfig ConfigData;
        private PushActivityPowerData MsgData;
        
        protected int timerCount; //倒计时数值
        protected bool isTimer = false;
        
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            
            m_TableViewV.GetItemCount = () =>
            {
                if (ConfigData == null || ConfigData.PowerRewards == null)
                {
                    return 0;
                }
                else
                {
                    return ConfigData.PowerRewards.Count;
                }
            };
            
            m_TableViewV.GetItemGo = () => m_goTaskItem;
            m_TableViewV.UpdateItemCell = UpdateTaskLogic;
            m_TableViewV.InitTableViewByIndex(0);
            BindBtnLogic(m_btnUp, () =>
            {
                if (!ToolScriptExtend.GetTable<activity_power>(out var data)) return;
                var minLevel = data.Min(x => x.player_level);
                var mainCity = GameEntry.LogicData.BuildingData.GetBuildingModuleById(101);
                var level = mainCity.LEVEL;
                if (level < minLevel) return;
                GameEntry.UI.OpenUIForm(EnumUIForm.UIWishLightTipForm);
            });
            
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                ChaoZhiManager.ShowActivityTip(TemplateId);
            });
            
            var bindData = GetBindData();
            if (bindData != null)
            {
                if (bindData is ActivityTime data)
                {
                    activityMsg = data;
                    TemplateId = (int)activityMsg.Template;
                    ConfigData = SpecialEventManager.GetWishLightConfig(TemplateId);
                    MsgData = SpecialEventManager.GetWishLightMsg(TemplateId);
                    InitPageView();
                    ChaoZhiManager.C2SLoadActivityInfo(activityMsg);
                }
            }
        }

        //再次打开
        public override void OnReOpen()
        {
            
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
        }


        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "SpecialEvent_WishLight")
            {
                if (param == 1)
                {
                    TemplateId = (int)activityMsg.Template;
                    ConfigData = SpecialEventManager.GetWishLightConfig(TemplateId);
                    MsgData = SpecialEventManager.GetWishLightMsg(TemplateId);
                    InitPageView();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                    m_txtTimer.text = "00:00:00";
                }
            }
        }

        //资源释放
        public override void Release()
        {
        }
        
        private void InitPageView()
        {
            if (MsgData == null || ConfigData == null) return;
            
            ShowMainReward();
            ShowProgressValue();
            
            //计算倒计时
            timerCount = ChaoZhiManager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);

        }
        
        //奖励列表
        private void ShowMainReward()
        {
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0);
            }
            else
            {
                m_TableViewV.ReloadData();
            }

            ScrollToRewardIndex();
            
        }
        
        //列表滑动到第一个可领取奖励的位置上,或者进行中的节点item
        private void ScrollToRewardIndex()
        {
            if (ConfigData == null || MsgData == null)
            {
                return;
            }

            var dataList = ConfigData.PowerRewards.ToList();
            
            dataList.Sort((a,b) =>
            {
                var statusA = SpecialEventManager.CheckWishLightItemStatus(MsgData, a);
                var statusB = SpecialEventManager.CheckWishLightItemStatus(MsgData, b);
                var flagA = GetCompareFlag(statusA);
                var flagB = GetCompareFlag(statusB);
                return flagA - flagB;
            });
            
            var result = dataList[0];
            var resultScore = result.Power;
            var resultStatus = SpecialEventManager.CheckWishLightItemStatus(MsgData,result);
            var index = ConfigData.PowerRewards.ToList().FindIndex(x => x.Power == resultScore);
            if (resultStatus == 0)
            {
                if (index - 1 < 0)
                {
                    index = 0;
                }
                else
                {
                    index--;
                }
            }
            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(DelayScroll(() =>
                {
                    m_TableViewV.MoveToTargetIndex(index,0.3f);
                }));
            }
        }

        IEnumerator DelayScroll(Action callback)
        {
            yield return delayTime;
            callback?.Invoke();
        }
        
        // 可领(1)>>未达标(2)>>已领取(3)
        private int GetCompareFlag(int status)
        {
            var flag = 3;
            //0:未达标 1：可领取 2：已领取
            if (status == 0)
            {
                flag = 2;
            }
            else if (status == 1)
            {
                flag = 1;
            }
            else if (status == 2)
            {
                flag = 3;
            }
            return flag;
        }
        
        private void ShowProgressValue()
        {
            var myPower = GameEntry.LogicData.RoleData.Power;
            var maxPower = ConfigData.PowerRewards.Max(x=>x.Power);

            if (myPower >= (ulong)maxPower)
            {
                m_sliderProgress.value = 1;
            }
            else
            {
                m_sliderProgress.value = myPower*1.0f / (ulong)maxPower;
            }
            m_txtValue.text = $"{myPower}/{maxPower}";
        }
        
         private void UpdateTaskLogic(int index, GameObject obj)
        {
            var config = ConfigData.PowerRewards[index];
            var root = obj.transform.Find("bg");
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnGo = root.Find("btnGo").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish").gameObject;
            var txt = root.Find("txt").GetComponent<UIText>();
            txt.text = $"{ToolScriptExtend.GetLang(1100492)} {config.Power}";
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");

            //0:未达标 1：可领取 2：已领取
            var status = SpecialEventManager.CheckWishLightItemStatus(MsgData, config);
            btnGo.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            
            //奖励列表
            var rewardList = config.Rewards.ToList();
            ToolScriptExtend.RecycleOrCreate(m_goReward, rewardRoot, rewardList.Count);
            ChaoZhiManager.ShowRewardList(m_goReward, rewardRoot, rewardList, 0.6f);
            scroll.enabled = rewardList.Count > 4;
            
            BindBtnLogic(btnGet, () =>
            {
                var reqData = new ActivityDrawReq
                {
                    Type = activityMsg.Type,
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    DrawId = config.Id
                };
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });
            BindBtnLogic(btnGo, () =>
            {
                Debug.Log("TODO");
            });
        }
    }
}